<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $serviceAvailable bool */
/* @var $campaigns array */
/* @var $queueStats array|null */
/* @var $regions app\common\models\Region[] */

$this->title = 'Массовые SMS рассылки';
$this->params['breadcrumbs'][] = $this->title;

// Подключаем Select2 для поиска клиентов
$this->registerJsFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', ['depends' => [\yii\web\JqueryAsset::class]]);
$this->registerCssFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');

// Подключаем iziToast для уведомлений
$this->registerJsFile('https://cdn.jsdelivr.net/npm/izitoast@1.4.0/dist/js/iziToast.min.js');
$this->registerCssFile('https://cdn.jsdelivr.net/npm/izitoast@1.4.0/dist/css/iziToast.min.css');
?>

<div class="bulk-sms-index">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sms"></i> <?= Html::encode($this->title) ?>
                    </h3>
                    <div class="card-tools">
                        <?php if ($serviceAvailable): ?>
                            <span class="badge badge-success">
                                <i class="fas fa-check-circle"></i> Сервис доступен
                            </span>
                        <?php else: ?>
                            <span class="badge badge-danger">
                                <i class="fas fa-exclamation-triangle"></i> Сервис недоступен
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">

                    <?php if (!$serviceAvailable): ?>
                        <div class="alert alert-danger">
                            <h5><i class="icon fas fa-ban"></i> Сервис недоступен!</h5>
                            Go микросервис массовых SMS недоступен. Проверьте:
                            <ul>
                                <li>Запущен ли Go сервис на порту 8080</li>
                                <li>Правильность настроек в params.php</li>
                                <li>Доступность сети между PHP и Go сервисом</li>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- Статистика очереди -->
                    <?php if ($queueStats): ?>
                        <div class="row mb-4">
                            <div class="col-md-2">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fas fa-clock"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">В ожидании</span>
                                        <span class="info-box-number"><?= $queueStats['pending'] ?? 0 ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="info-box">
                                    <span class="info-box-icon bg-warning"><i class="fas fa-spinner"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Обрабатывается</span>
                                        <span class="info-box-number"><?= $queueStats['processing'] ?? 0 ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Отправлено</span>
                                        <span class="info-box-number"><?= $queueStats['sent'] ?? 0 ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="fas fa-check-double"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Доставлено</span>
                                        <span class="info-box-number"><?= $queueStats['delivered'] ?? 0 ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="info-box">
                                    <span class="info-box-icon bg-danger"><i class="fas fa-times"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Ошибки</span>
                                        <span class="info-box-number"><?= $queueStats['failed'] ?? 0 ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="info-box">
                                    <span class="info-box-icon bg-secondary"><i class="fas fa-ban"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Отменено</span>
                                        <span class="info-box-number"><?= $queueStats['cancelled'] ?? 0 ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Навигация -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createCampaignModal" <?= !$serviceAvailable ? 'disabled' : '' ?>>
                                    <i class="fas fa-plus"></i> Создать кампанию
                                </button>
                                <button type="button" class="btn btn-info" data-toggle="modal" data-target="#createByFilterModal" <?= !$serviceAvailable ? 'disabled' : '' ?>>
                                    <i class="fas fa-filter"></i> По фильтрам
                                </button>
                                <?= Html::a('<i class="fas fa-list"></i> Все кампании', ['campaigns'], ['class' => 'btn btn-secondary']) ?>
                                <?= Html::a('<i class="fas fa-chart-bar"></i> Статистика', ['stats'], ['class' => 'btn btn-success']) ?>
                            </div>
                        </div>
                    </div>

                    <!-- Последние кампании -->
                    <?php if (!empty($campaigns)): ?>
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Последние кампании</h3>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Название</th>
                                            <th>Получателей</th>
                                            <th>Отправлено</th>
                                            <th>Статус</th>
                                            <th>Создана</th>
                                            <th>Действия</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($campaigns as $campaign): ?>
                                            <tr>
                                                <td><?= $campaign['id'] ?></td>
                                                <td><?= Html::encode($campaign['name']) ?></td>
                                                <td><?= $campaign['total_recipients'] ?></td>
                                                <td>
                                                    <span class="badge badge-success"><?= $campaign['sent_count'] ?></span>
                                                    <?php if ($campaign['failed_count'] > 0): ?>
                                                        <span class="badge badge-danger"><?= $campaign['failed_count'] ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusLabels = [
                                                        0 => '<span class="badge badge-secondary">Создана</span>',
                                                        1 => '<span class="badge badge-warning">Обрабатывается</span>',
                                                        2 => '<span class="badge badge-success">Завершена</span>',
                                                        3 => '<span class="badge badge-secondary">Отменена</span>',
                                                        4 => '<span class="badge badge-danger">Ошибка</span>',
                                                    ];
                                                    echo $statusLabels[$campaign['status']] ?? '<span class="badge badge-light">Неизвестно</span>';
                                                    ?>
                                                </td>
                                                <td><?= date('d.m.Y H:i', strtotime($campaign['created_at'])) ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-info btn-sm" onclick="showCampaignStatus(<?= $campaign['id'] ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <?php if ($campaign['status'] == 0): ?>
                                                            <button class="btn btn-success btn-sm" onclick="startCampaign(<?= $campaign['id'] ?>)">
                                                                <i class="fas fa-play"></i>
                                                            </button>
                                                        <?php elseif ($campaign['status'] == 1): ?>
                                                            <button class="btn btn-warning btn-sm" onclick="stopCampaign(<?= $campaign['id'] ?>)">
                                                                <i class="fas fa-stop"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> Нет кампаний</h5>
                            Создайте первую кампанию массовых SMS рассылок.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно создания кампании -->
<div class="modal fade" id="createCampaignModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Создать кампанию массовых SMS</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="createCampaignForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="campaign-name">Название кампании *</label>
                        <input type="text" class="form-control" id="campaign-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="campaign-message">Текст сообщения *</label>
                        <textarea class="form-control" id="campaign-message" name="message" rows="4" maxlength="1000" required></textarea>
                        <small class="form-text text-muted">Максимум 1000 символов</small>
                    </div>
                    <div class="form-group">
                        <label for="campaign-clients">Получатели *</label>
                        <select class="form-control" id="campaign-clients" name="client_ids[]" multiple required>
                        </select>
                        <small class="form-text text-muted">Начните вводить имя клиента для поиска</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-primary">Создать кампанию</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Модальное окно создания кампании по фильтрам -->
<div class="modal fade" id="createByFilterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Создать кампанию по фильтрам</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form id="createByFilterForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="filter-name">Название кампании *</label>
                        <input type="text" class="form-control" id="filter-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="filter-message">Текст сообщения *</label>
                        <textarea class="form-control" id="filter-message" name="message" rows="4" maxlength="1000" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="filter-regions">Регионы</label>
                        <select class="form-control" id="filter-regions" name="region_ids[]" multiple>
                            <?php foreach ($regions as $region): ?>
                                <option value="<?= $region->id ?>"><?= Html::encode($region->name) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter-status">Статус клиентов</label>
                        <select class="form-control" id="filter-status" name="status">
                            <option value="1" selected>Активные</option>
                            <option value="0">Неактивные</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-primary">Создать кампанию</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$this->registerJs("
// Инициализация Select2 для поиска клиентов
$('#campaign-clients').select2({
    ajax: {
        url: '" . Url::to(['search-clients']) . "',
        dataType: 'json',
        delay: 250,
        data: function (params) {
            return {
                q: params.term,
                limit: 20
            };
        },
        processResults: function (data) {
            return {
                results: data.results
            };
        },
        cache: true
    },
    placeholder: 'Начните вводить имя клиента...',
    minimumInputLength: 2,
    width: '100%'
});

// Инициализация Select2 для регионов
$('#filter-regions').select2({
    placeholder: 'Выберите регионы...',
    width: '100%'
});

// Обработка формы создания кампании
$('#createCampaignForm').on('submit', function(e) {
    e.preventDefault();

    var formData = $(this).serialize();
    var submitBtn = $(this).find('button[type=submit]');

    submitBtn.prop('disabled', true).html('<i class=\"fas fa-spinner fa-spin\"></i> Создание...');

    $.ajax({
        url: '" . Url::to(['create-campaign']) . "',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                iziToast.success({
                    title: 'Успех',
                    message: response.message + ' (ID: ' + response.campaign_id + ', получателей: ' + response.recipients_count + ')'
                });
                $('#createCampaignModal').modal('hide');
                location.reload();
            } else {
                iziToast.error({
                    title: 'Ошибка',
                    message: response.message
                });
            }
        },
        error: function() {
            iziToast.error({
                title: 'Ошибка',
                message: 'Произошла ошибка при создании кампании'
            });
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('Создать кампанию');
        }
    });
});

// Обработка формы создания кампании по фильтрам
$('#createByFilterForm').on('submit', function(e) {
    e.preventDefault();

    var formData = $(this).serialize();
    var submitBtn = $(this).find('button[type=submit]');

    submitBtn.prop('disabled', true).html('<i class=\"fas fa-spinner fa-spin\"></i> Создание...');

    $.ajax({
        url: '" . Url::to(['create-campaign-by-filter']) . "',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                iziToast.success({
                    title: 'Успех',
                    message: response.message + ' (ID: ' + response.campaign_id + ', получателей: ' + response.recipients_count + ')'
                });
                $('#createByFilterModal').modal('hide');
                location.reload();
            } else {
                iziToast.error({
                    title: 'Ошибка',
                    message: response.message
                });
            }
        },
        error: function() {
            iziToast.error({
                title: 'Ошибка',
                message: 'Произошла ошибка при создании кампании'
            });
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('Создать кампанию');
        }
    });
});

// Функция показа статуса кампании
function showCampaignStatus(campaignId) {
    $.ajax({
        url: '" . Url::to(['campaign-status']) . "',
        type: 'GET',
        data: {id: campaignId},
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var campaign = response.data.campaign;
                var queueStatus = response.data.queue_status;

                var content = '<div class=\"row\">' +
                    '<div class=\"col-md-6\">' +
                        '<h5>Информация о кампании</h5>' +
                        '<p><strong>Название:</strong> ' + campaign.name + '</p>' +
                        '<p><strong>Получателей:</strong> ' + campaign.total_recipients + '</p>' +
                        '<p><strong>Отправлено:</strong> ' + campaign.sent_count + '</p>' +
                        '<p><strong>Доставлено:</strong> ' + campaign.delivered_count + '</p>' +
                        '<p><strong>Ошибок:</strong> ' + campaign.failed_count + '</p>' +
                    '</div>' +
                    '<div class=\"col-md-6\">' +
                        '<h5>Статус очереди</h5>' +
                        '<p><strong>В ожидании:</strong> ' + queueStatus.pending + '</p>' +
                        '<p><strong>Обрабатывается:</strong> ' + queueStatus.processing + '</p>' +
                        '<p><strong>Отправлено:</strong> ' + queueStatus.sent + '</p>' +
                        '<p><strong>Доставлено:</strong> ' + queueStatus.delivered + '</p>' +
                        '<p><strong>Ошибок:</strong> ' + queueStatus.failed + '</p>' +
                    '</div>' +
                '</div>';

                iziToast.info({
                    title: 'Статус кампании #' + campaignId,
                    message: content,
                    timeout: 10000,
                    layout: 2
                });
            } else {
                iziToast.error({
                    title: 'Ошибка',
                    message: response.message
                });
            }
        },
        error: function() {
            iziToast.error({
                title: 'Ошибка',
                message: 'Не удалось получить статус кампании'
            });
        }
    });
}

// Функция запуска кампании
function startCampaign(campaignId) {
    if (!confirm('Запустить кампанию #' + campaignId + '?')) {
        return;
    }

    $.ajax({
        url: '" . Url::to(['start-campaign']) . "',
        type: 'POST',
        data: {id: campaignId},
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                iziToast.success({
                    title: 'Успех',
                    message: response.message
                });
                location.reload();
            } else {
                iziToast.error({
                    title: 'Ошибка',
                    message: response.message
                });
            }
        },
        error: function() {
            iziToast.error({
                title: 'Ошибка',
                message: 'Не удалось запустить кампанию'
            });
        }
    });
}

// Функция остановки кампании
function stopCampaign(campaignId) {
    if (!confirm('Остановить кампанию #' + campaignId + '?')) {
        return;
    }

    $.ajax({
        url: '" . Url::to(['stop-campaign']) . "',
        type: 'POST',
        data: {id: campaignId},
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                iziToast.success({
                    title: 'Успех',
                    message: response.message
                });
                location.reload();
            } else {
                iziToast.error({
                    title: 'Ошибка',
                    message: response.message
                });
            }
        },
        error: function() {
            iziToast.error({
                title: 'Ошибка',
                message: 'Не удалось остановить кампанию'
            });
        }
    });
}

// Очистка форм при закрытии модальных окон
$('#createCampaignModal').on('hidden.bs.modal', function() {
    $('#createCampaignForm')[0].reset();
    $('#campaign-clients').val(null).trigger('change');
});

$('#createByFilterModal').on('hidden.bs.modal', function() {
    $('#createByFilterForm')[0].reset();
    $('#filter-regions').val(null).trigger('change');
});
");
?>