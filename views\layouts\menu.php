<?php

use yii\helpers\Html;
use yii\helpers\Url;
use app\common\models\Client;

//$client = new Client();
//$schedule = $client->getSettings('schedule');
//$kpiAfterHowMuchWater = $client->getSettings('kpiAfterHowMuchWater');

?>

<div class="navbar-bg" style="height: 70px;"></div>
<nav class="navbar navbar-expand-lg main-navbar">
    <a href="" class="navbar-brand sidebar-gone-hide">Carpet</a>
    <a href="#" class="nav-link sidebar-gone-show" data-toggle="sidebar"><i class="fas fa-bars"></i></a>


    <div class="nav-collapse">
        <a class="sidebar-gone-show nav-collapse-toggle nav-link" href="#">
            <i class="fas fa-ellipsis-v"></i>
        </a>


        <!--excel-->



        <!--        dashboard-->


        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span>
                        <?php echo Yii::t('app', 'dashboard') ?>
                    </span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/dashboard/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'dashboard') ?></a>
                    <a href="<?php echo Url::to(['/backend/dashboard-v2/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'dashboard2') ?></a>
                    <a href="<?php echo Url::to(['/backend/dashboard-table/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'dashboard-table') ?></a>

                </div>
            </li>
        </ul>

        <!--        cashier-->

        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span>
                        <?php echo Yii::t('app', 'process') ?>
                    </span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/cashier/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'Not Accepted Orders') ?></a>
                    <a href="<?php echo Url::to(['/backend/process/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'orders') ?></a>
                    <a href="<?php echo Url::to(['/backend/product-return/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'product_return') ?></a>
                    <a href="<?php echo Url::to(['/backend/tracking/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'tracking') ?></a>
                    <a href="<?php echo Url::to(['/backend/suplier-sales/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'suplier_sales') ?></a>
                </div>
            </li>
        </ul>
        <!--        cashier-->
        <!--clients-->
        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span>
                        <?php echo Yii::t('app', 'clients') ?>
                    </span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/client/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'all_clients') ?></a>
                    <a href="<?php echo Url::to(['/backend/client/debt']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'debt_clients') ?></a>
                    <a href="<?php echo Url::to(['/backend/bulk-sms/index']); ?>" class="dropdown-item">
                        <i class="fas fa-sms"></i> SMS рассылка
                    </a>
                    <a href="<?php echo Url::to(['/backend/bulk-sms/campaigns']); ?>" class="dropdown-item">
                        <i class="fas fa-chart-bar"></i> Статистика SMS
                    </a>
                </div>
            </li>
        </ul>


        <!--       expenses-->
        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span>
                        <?php echo Yii::t('app', 'expenses') ?>
                    </span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/expenses/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'all_expenses') ?></a>
                    <a href="<?php echo Url::to(['/backend/expenses/type']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'type_expenses') ?></a>
                </div>
            </li>
        </ul>

        <!--      storage  -->
        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span>
                        <?php echo Yii::t('app', 'storage') ?>
                    </span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/storage/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'All Storages') ?></a>
                    <a href="<?php echo Url::to(['/backend/product/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'all_product') ?></a>
                    <a href="<?php echo Url::to(['/backend/storage/invoices']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'invoices') ?></a>
                    <a href="<?php echo Url::to(['/backend/storage/view']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'input_output') ?></a>
                    <a href="<?php echo Url::to(['/backend/storage/remains']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'remains') ?></a>
                    <a href="<?php echo Url::to(['/backend/transfer/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'transfer_index') ?></a>
                    <a href="<?php echo Url::to(['/backend/supplier-return/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'return_to_supplier') ?></a>
                </div>
            </li>
        </ul>

        <!--      raport  -->
        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span><?php echo Yii::t('app', 'report') ?></span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/report/income']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'report_income') ?></a>
                    <a href="<?php echo Url::to(['/backend/report/sells']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'sell') ?></a>
                    <a href="<?php echo Url::to(['/backend/seller-report/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'Отчет по продавцам') ?></a>
                    <a href="<?php echo Url::to(['/backend/rating/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'rating_clients') ?></a>
                    <a href="<?php echo Url::to(['/backend/report/detail']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'detail') ?></a>
                    <a href="<?php echo Url::to(['/backend/report/top-sells-products']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'top_sells_product') ?></a>
                    <a href="<?php echo Url::to(['/backend/report/all-products']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'all_products') ?></a>
                </div>
            </li>
        </ul>

        <!--      finance  -->
        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span><?php echo Yii::t('app', 'finance') ?></span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/currency/view']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'payment_type') ?></a>
                    <a href="<?php echo Url::to(['/backend/main-cashbox/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'main_cashbox') ?></a>
                    <a href="<?php echo Url::to(['/backend/cashbox/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'cashbox') ?></a>
                    <a href="<?php echo Url::to(['/backend/payment/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'payment') ?></a>
                    <a href="<?php echo Url::to(['/backend/price/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'price_') ?></a>

                </div>
            </li>
        </ul>



        <!-- Dropdown for settings -->
        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span><?php echo Yii::t('app', 'settings') ?></span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/user/users']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'users') ?></a>
                    <a href="<?php echo Url::to(['/backend/suplier/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'suplier') ?></a>
                    <a href="<?php echo Url::to(['/backend/sizes/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'sizes') ?></a>
                    <a href="<?php echo Url::to(['/backend/color/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'colors') ?></a>
                    <a href="<?php echo Url::to(['/backend/log/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'log') ?></a>
                    <a href="<?php echo Url::to(['/backend/design/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'design') ?></a>
                    <a href="<?php echo Url::to(['/backend/worker/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'worker_men') ?></a>
                    <a href="<?php echo Url::to(['/backend/position/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'position') ?></a>
                    <a href="<?php echo Url::to(['/backend/notification/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'notification') ?></a>
                    <a href="<?php echo Url::to(['/backend/region/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'region') ?></a>

                </div>
            </li>
        </ul>
        <!-- End settings dropdown -->


        <!-- Dropdown for employee -->
        <ul class="navbar-nav">
            <li class="nav-item dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link text-info dropdown-toggle" aria-expanded="false">
                    <span><?php echo Yii::t('app', 'employee') ?></span>
                </a>
                <div class="dropdown-menu">
                    <a href="<?php echo Url::to(['/backend/employee/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'employee') ?></a>
                    <a href="<?php echo Url::to(['/backend/employee-finance/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'employee_finance') ?></a>
                    <a href="<?php echo Url::to(['/backend/employee-debt/index']); ?>" class="dropdown-item"><?php echo Yii::t('app', 'employee_debt') ?></a>
                </div>
            </li>
        </ul>
        <!-- End employee dropdown -->


    </div>


    <style>
        .dropdown-item {
            white-space: normal;
            word-wrap: break-word;
            min-width: 200px;
            max-width: 300px;
        }
    </style>

    <ul class="navbar-nav navbar-right ml-auto">
        <li class="dropdown">
            <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">
                <div class="d-sm-none d-lg-inline-block">
                    <?php echo ((isset(Yii::$app->user->identity)) ? Yii::$app->user->identity->username : '') ?>
                </div>
            </a>
            <div class="dropdown-menu dropdown-menu-right">
                <?= Yii::$app->user->isGuest
                    ? Html::a('<i class="fas fa-sign-out-alt"></i> Login', ['/site/login'], ['class' => 'dropdown-item has-icon text-danger'])
                    : Html::beginForm(['/site/logout'], 'post', ['class' => 'dropdown-item p-0'])
                    . Html::submitButton(
                        '<i class="fas fa-sign-out-alt"></i> ' . Yii::t('app', 'logout') . ' (' . Yii::$app->user->identity->username . ')',
                        ['class' => 'btn btn-link text-danger w-100 text-left']
                    )
                    . Html::endForm()
                ?>
            </div>
        </li>
    </ul>
</nav>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle dropdown toggles
        document.querySelectorAll('.dropdown-toggle').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const dropdownMenu = this.nextElementSibling;
                const isCurrentlyShown = dropdownMenu.classList.contains('show');

                // Close all other dropdowns
                document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                    menu.classList.remove('show');
                    menu.previousElementSibling.setAttribute('aria-expanded', 'false');
                });

                // Toggle current dropdown
                if (!isCurrentlyShown) {
                    dropdownMenu.classList.add('show');
                    this.setAttribute('aria-expanded', 'true');
                }
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                    menu.classList.remove('show');
                    menu.previousElementSibling.setAttribute('aria-expanded', 'false');
                });
            }
        });

        // Prevent dropdown menu clicks from closing the dropdown
        document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
            menu.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    });
</script>