<?php

use yii\helpers\Html;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $campaigns app\common\models\SmsCampaign[] */
/* @var $totalStats array */
/* @var $statusStats array */
/* @var $dailyStats array */

$this->title = 'Статистика SMS кампаний';
$this->params['breadcrumbs'][] = ['label' => 'SMS рассылка', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="card-body">
    <div class="row">
        <div class="col-md-12">
                <div>
                    <h3 class="card-title">
                        <?= Html::encode($this->title) ?>
                    </h3>
                </div>

                    

                    <!-- Список кампаний -->
                      
                        <div class="card-body p-0">
                            <?php if (!empty($campaigns)): ?>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Название</th>
                                            <th>Сообщение</th>
                                            <th>Шаблон</th>
                                            <th>Получателей</th>
                                            <th>Отправлено</th>
                                            <th>Доставлено</th>
                                            <th>Ошибок</th>
                                            <th>Успешность</th>
                                            <th>Статус</th>
                                            <th>Создана</th>
                                            <th>Действия</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($campaigns as $campaign): ?>
                                            <tr>
                                                <td><?= $campaign->id ?></td>
                                                <td><?= Html::encode($campaign->name) ?></td>
                                                <td>
                                                    <span class="text-muted" title="<?= Html::encode($campaign->message) ?>">
                                                        <?= Html::encode(mb_substr($campaign->message, 0, 50)) ?>
                                                        <?= mb_strlen($campaign->message) > 50 ? '...' : '' ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($campaign->template_used): ?>
                                                        <span class="badge badge-success">Да</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-secondary">Нет</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= number_format($campaign->total_recipients) ?></td>
                                                <td>
                                                    <span class="badge badge-info"><?= number_format($campaign->sent_count) ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-success"><?= number_format($campaign->delivered_count) ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($campaign->failed_count > 0): ?>
                                                        <span class="badge badge-danger"><?= number_format($campaign->failed_count) ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">0</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?= $campaign->getSuccessRate() >= 90 ? 'success' : ($campaign->getSuccessRate() >= 70 ? 'warning' : 'danger') ?>">
                                                        <?= $campaign->getSuccessRate() ?>%
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?= $campaign->getStatusClass() ?>">
                                                        <?= $campaign->getStatusLabel() ?>
                                                    </span>
                                                </td>
                                                <td><?= date('d.m.Y H:i', strtotime($campaign->created_at)) ?></td>
                                                <td>
                                                    <?= Html::a('<i class="fas fa-eye"></i>', ['campaign-details', 'id' => $campaign->id], [
                                                        'class' => 'btn btn-sm btn-info',
                                                        'title' => 'Детали кампании'
                                                    ]) ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php else: ?>
                                <div>
                                    <h> <?= Yii::t('app', 'No data available.') ?></h>
                                </div>
                            <?php endif; ?>
                        </div>

                 

        </div>
    </div>
</div>
