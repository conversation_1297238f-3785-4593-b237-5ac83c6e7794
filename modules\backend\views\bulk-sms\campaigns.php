<?php

use yii\helpers\Html;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $campaigns app\common\models\SmsCampaign[] */
/* @var $totalStats array */
/* @var $statusStats array */
/* @var $dailyStats array */

$this->title = 'Статистика SMS кампаний';
$this->params['breadcrumbs'][] = ['label' => 'SMS рассылка', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="sms-campaigns-stats">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        <?= Html::encode($this->title) ?>
                    </h3>
                    <div class="card-tools">
                        <?= Html::a('<i class="fas fa-arrow-left"></i> Назад к рассылке', ['index'], ['class' => 'btn btn-sm btn-secondary']) ?>
                    </div>
                </div>
                <div class="card-body">
                    
                    <!-- Общая статистика -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-bullhorn"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Всего кампаний</span>
                                    <span class="info-box-number"><?= $totalStats['total_campaigns'] ?? 0 ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Получателей</span>
                                    <span class="info-box-number"><?= number_format($totalStats['total_recipients'] ?? 0) ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-paper-plane"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Отправлено</span>
                                    <span class="info-box-number"><?= number_format($totalStats['total_sent'] ?? 0) ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check-double"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Доставлено</span>
                                    <span class="info-box-number"><?= number_format($totalStats['total_delivered'] ?? 0) ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Ошибок</span>
                                    <span class="info-box-number"><?= number_format($totalStats['total_failed'] ?? 0) ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-percentage"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Успешность</span>
                                    <span class="info-box-number">
                                        <?php 
                                        $successRate = 0;
                                        if (($totalStats['total_recipients'] ?? 0) > 0) {
                                            $successRate = round((($totalStats['total_sent'] ?? 0) / $totalStats['total_recipients']) * 100, 1);
                                        }
                                        echo $successRate . '%';
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Список кампаний -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Последние кампании</h3>
                        </div>
                        <div class="card-body p-0">
                            <?php if (!empty($campaigns)): ?>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Название</th>
                                            <th>Сообщение</th>
                                            <th>Шаблон</th>
                                            <th>Получателей</th>
                                            <th>Отправлено</th>
                                            <th>Доставлено</th>
                                            <th>Ошибок</th>
                                            <th>Успешность</th>
                                            <th>Статус</th>
                                            <th>Создана</th>
                                            <th>Действия</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($campaigns as $campaign): ?>
                                            <tr>
                                                <td><?= $campaign->id ?></td>
                                                <td><?= Html::encode($campaign->name) ?></td>
                                                <td>
                                                    <span class="text-muted" title="<?= Html::encode($campaign->message) ?>">
                                                        <?= Html::encode(mb_substr($campaign->message, 0, 50)) ?>
                                                        <?= mb_strlen($campaign->message) > 50 ? '...' : '' ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($campaign->template_used): ?>
                                                        <span class="badge badge-success">Да</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-secondary">Нет</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= number_format($campaign->total_recipients) ?></td>
                                                <td>
                                                    <span class="badge badge-info"><?= number_format($campaign->sent_count) ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-success"><?= number_format($campaign->delivered_count) ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($campaign->failed_count > 0): ?>
                                                        <span class="badge badge-danger"><?= number_format($campaign->failed_count) ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">0</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?= $campaign->getSuccessRate() >= 90 ? 'success' : ($campaign->getSuccessRate() >= 70 ? 'warning' : 'danger') ?>">
                                                        <?= $campaign->getSuccessRate() ?>%
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?= $campaign->getStatusClass() ?>">
                                                        <?= $campaign->getStatusLabel() ?>
                                                    </span>
                                                </td>
                                                <td><?= date('d.m.Y H:i', strtotime($campaign->created_at)) ?></td>
                                                <td>
                                                    <?= Html::a('<i class="fas fa-eye"></i>', ['campaign-details', 'id' => $campaign->id], [
                                                        'class' => 'btn btn-sm btn-info',
                                                        'title' => 'Детали кампании'
                                                    ]) ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php else: ?>
                                <div class="alert alert-info m-3">
                                    <h5><i class="icon fas fa-info"></i> Нет данных</h5>
                                    Пока не было создано ни одной SMS кампании.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Статистика по дням -->
                    <?php if (!empty($dailyStats)): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3 class="card-title">Статистика по дням (последние 30 дней)</h3>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Дата</th>
                                            <th>Кампаний</th>
                                            <th>Отправлено SMS</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($dailyStats as $stat): ?>
                                            <tr>
                                                <td><?= date('d.m.Y', strtotime($stat['date'])) ?></td>
                                                <td><?= $stat['campaigns_count'] ?></td>
                                                <td><?= number_format($stat['sent_count']) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
</div>
