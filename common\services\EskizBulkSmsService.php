<?php

namespace app\common\services;

use app\common\models\Clients;
use Yii;

/**
 * Сервис для массовой отправки SMS через API Eskiz
 */
class EskizBulkSmsService
{
    private $message_template;

    public function __construct()
    {
        $this->message_template = Yii::t("app2", "sms_code_message") . ": ";
    }

    /**
     * Массовая отправка SMS через API Eskiz
     * @param string $message Текст сообщения
     * @param array $recipients Массив получателей [['client_id' => 1, 'phone' => '+998...', 'name' => 'Имя']]
     * @param bool $useTemplate Использовать ли шаблон
     * @return array Результат отправки
     * @throws \Exception
     */
    public function sendBulkSms(string $message, array $recipients, bool $useTemplate = true): array
    {
        try {
            // Получаем токен из кеша или генерируем новый
            $token = Yii::$app->cache->getOrSet('eskiz-bearer-token', function () {
                try {
                    $response = $this->login();
                    if (!empty($response) && isset($response['data']) && isset($response['data']['token'])) {
                        return $response['data']['token'];
                    }
                } catch (\Exception $e) {
                    throw $e;
                }
                throw new \Exception('Не удалось получить токен от Eskiz');
            });

            // Подготавливаем сообщение
            $finalMessage = $message;
            if ($useTemplate) {
                // Получаем одобренный шаблон
                $templateText = $this->getApprovedTemplate($token);

                // Если шаблон содержит %w, заменяем его на сообщение
                if (strpos($templateText, '%w') !== false) {
                    $finalMessage = str_replace('%w', $message, $templateText);
                } else {
                    // Если %w нет, добавляем сообщение в конец шаблона
                    $finalMessage = $templateText . "\n\n" . $message;
                }

                Yii::info("Используем шаблон: $templateText", 'bulk-sms');
                Yii::info("Финальное сообщение: $finalMessage", 'bulk-sms');
            }

            // Подготавливаем данные для массовой отправки
            $messages = [];
            foreach ($recipients as $recipient) {
                $phone = $this->formatPhone($recipient['phone']);
                if ($this->validatePhone($phone)) {
                    // Убираем + для API Eskiz
                    $phoneForApi = str_replace('+', '', $phone);
                    $messages[] = [
                        'user_sms_id' => $recipient['client_id'] ?? uniqid(),
                        'to' => $phoneForApi,
                        'text' => $finalMessage,
                    ];
                }
            }

            if (empty($messages)) {
                throw new \Exception('Нет валидных получателей для отправки');
            }

            // Отправляем через batch API
            $result = $this->sendBatch($token, $messages);

            // Логируем результат
            Yii::info([
                'action' => 'eskiz_bulk_sms_sent',
                'recipients_count' => count($messages),
                'message_length' => strlen($finalMessage),
                'result' => $result,
            ], 'bulk-sms');

            return [
                'success' => true,
                'sent_count' => count($messages),
                'result' => $result,
                'message' => 'SMS отправлены успешно',
            ];
        } catch (\Exception $e) {
            Yii::error('Ошибка массовой отправки SMS через Eskiz: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при массовой отправке SMS: ' . $e->getMessage());
        }
    }

    /**
     * Создание кампании по фильтрам клиентов
     * @param string $name Название кампании
     * @param string $message Текст сообщения
     * @param array $filters Фильтры клиентов
     * @param bool $useTemplate Использовать ли шаблон
     * @return array
     * @throws \Exception
     */
    public function sendBulkSmsByFilter(string $name, string $message, array $filters, bool $useTemplate = true): array
    {
        try {
            // Получаем клиентов по фильтрам
            $query = Clients::find()
                ->where(['deleted_at' => null])
                ->andWhere(['!=', 'phone_number', ''])
                ->andWhere(['!=', 'phone_number', null]);

            // Применяем фильтры
            if (isset($filters['status'])) {
                $query->andWhere(['status' => $filters['status']]);
            }

            if (isset($filters['region_ids']) && !empty($filters['region_ids'])) {
                $query->andWhere(['region_id' => $filters['region_ids']]);
            }

            $clients = $query->all();

            if (empty($clients)) {
                throw new \Exception('Не найдено клиентов по указанным фильтрам');
            }

            // Формируем список получателей
            $recipients = [];
            foreach ($clients as $client) {
                $recipients[] = [
                    'client_id' => $client->id,
                    'phone' => $client->phone_number,
                    'name' => $client->full_name ?: 'Клиент',
                ];
            }

            // Отправляем SMS
            $result = $this->sendBulkSms($message, $recipients, $useTemplate);
            $result['campaign_name'] = $name;
            $result['total_recipients'] = count($recipients);

            return $result;
        } catch (\Exception $e) {
            Yii::error('Ошибка отправки SMS по фильтру: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при отправке SMS по фильтру: ' . $e->getMessage());
        }
    }

    /**
     * Отправка batch SMS через API Eskiz
     * @param string $token Токен авторизации
     * @param array $messages Массив сообщений
     * @return array
     * @throws \Exception
     */
    private function sendBatch(string $token, array $messages): array
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => 'https://notify.eskiz.uz/api/message/sms/send-batch',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60, // Увеличиваем timeout для массовой отправки
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_POSTFIELDS => json_encode([
                'messages' => $messages,
                'from' => Yii::$app->params['smsNickName'] ?? 'CARPET',
                'dispatch_id' => time(), // Уникальный ID для группы сообщений
            ]),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $token,
                'Content-Type: application/json',
                'Accept: application/json',
            ],
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            $error = curl_error($curl);
            curl_close($curl);
            throw new \Exception('ESKIZ cURL error: ' . $error);
        }

        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        $responseData = json_decode($response, true);

        // Проверяем, не истек ли токен (401 или сообщение об истечении)
        if (
            $httpCode === 401 ||
            (isset($responseData['status']) && $responseData['status'] === false &&
                (isset($responseData['message']) && ($responseData['message'] === 'Expired' || strpos($responseData['message'], 'token') !== false)))
        ) {

            Yii::$app->cache->delete('eskiz-bearer-token');

            // Получаем новый токен и повторяем запрос
            $newToken = Yii::$app->cache->getOrSet('eskiz-bearer-token', function () {
                $response = $this->login();
                return $response['data']['token'];
            });

            return $this->sendBatch($newToken, $messages);
        }

        if ($httpCode >= 400) {
            throw new \Exception('HTTP error ' . $httpCode . ': ' . ($responseData['message'] ?? $response));
        }

        return $responseData;
    }

    /**
     * Получение одобренного шаблона SMS
     * @param string $token Токен авторизации
     * @return string Текст шаблона
     * @throws \Exception
     */
    private function getApprovedTemplate($token)
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => 'https://notify.eskiz.uz/api/user/templates',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $token,
            ],
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            $error = curl_error($curl);
            curl_close($curl);
            throw new \Exception('ESKIZ cURL error при получении шаблонов: ' . $error);
        }

        curl_close($curl);

        $templatesData = json_decode($response, true);

        // Добавляем отладочную информацию
        Yii::info('Templates API response: ' . $response, 'bulk-sms');

        if (!isset($templatesData['result']) || empty($templatesData['result'])) {
            throw new \Exception('Нет доступных шаблонов. Ответ API: ' . $response);
        }

        // Ищем любой шаблон со статусом "reklama" (одобренный)
        foreach ($templatesData['result'] as $template) {
            if (
                isset($template['status']) && $template['status'] === 'reklama' &&
                isset($template['template'])
            ) {
                return $template['template'];
            }
        }

        throw new \Exception('Нет одобренных шаблонов');
    }

    /**
     * Авторизация в API Eskiz
     * @return array
     * @throws \Exception
     */
    private function login()
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => 'https://notify.eskiz.uz/api/auth/login',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_POSTFIELDS => [
                'email' => Yii::$app->params['eskizSmsServiceLogin'],
                'password' => Yii::$app->params['eskizSmsServicePassword'],
            ],
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            curl_close($curl);
            throw new \Exception('ESKIZ cURL error при логине: ' . curl_error($curl));
        }

        $responseData = json_decode($response, true);
        curl_close($curl);

        if (!is_array($responseData) || !isset($responseData['data']) || !isset($responseData['data']['token'])) {
            throw new \Exception('Неверный формат ответа от Eskiz при логине: ' . $response);
        }

        return $responseData;
    }

    /**
     * Валидация номера телефона
     * @param string $phone
     * @return bool
     */
    public function validatePhone(string $phone): bool
    {
        $pattern = Yii::$app->params['phone_number_regex'] ?? '/^\+998(90|91|93|94|95|97|98|99|50|88|77|33|20)[0-9]{7}$/';
        return preg_match($pattern, $phone);
    }

    /**
     * Форматирование номера телефона
     * @param string $phone
     * @return string
     */
    public function formatPhone(string $phone): string
    {
        // Удаляем все символы кроме цифр и +
        $phone = preg_replace('/[^\d+]/', '', $phone);

        // Если номер начинается с 998, добавляем +
        if (preg_match('/^998\d{9}$/', $phone)) {
            $phone = '+' . $phone;
        }

        // Если номер начинается с 8 или 9, добавляем +998
        if (preg_match('/^[89]\d{8}$/', $phone)) {
            $phone = '+998' . substr($phone, 1);
        }

        return $phone;
    }

    /**
     * Проверка доступности API Eskiz
     * @return bool
     */
    public function isServiceAvailable(): bool
    {
        try {
            $this->login();
            return true;
        } catch (\Exception $e) {
            Yii::error('Eskiz API недоступен: ' . $e->getMessage(), 'bulk-sms');
            return false;
        }
    }
}
