<?php

// Простой тест для проверки API Eskiz массовой отправки

// Конфигурация (замените на ваши данные)
$eskizLogin = '<EMAIL>';
$eskizPassword = 'your_password';
$smsNickName = 'CARPET';

// Функция для логина в API Eskiz
function eskizLogin($email, $password) {
    $curl = curl_init();

    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/auth/login',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => [
            'email' => $email,
            'password' => $password,
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
    if (curl_errno($curl)) {
        curl_close($curl);
        throw new Exception('cURL error: ' . curl_error($curl));
    }

    curl_close($curl);
    
    $responseData = json_decode($response, true);
    
    if ($httpCode !== 200 || !isset($responseData['data']['token'])) {
        throw new Exception('Login failed: ' . $response);
    }
    
    return $responseData['data']['token'];
}

// Функция для отправки batch SMS
function sendBatchSms($token, $messages, $from) {
    $curl = curl_init();

    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/message/sms/send-batch',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 60,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => json_encode([
            'messages' => $messages,
            'from' => $from,
        ]),
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json',
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
    if (curl_errno($curl)) {
        curl_close($curl);
        throw new Exception('cURL error: ' . curl_error($curl));
    }

    curl_close($curl);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
    
    return json_decode($response, true);
}

// Функция для отправки одиночного SMS (для сравнения)
function sendSingleSms($token, $phone, $message, $from) {
    $curl = curl_init();

    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://notify.eskiz.uz/api/message/sms/send',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_POSTFIELDS => [
            'mobile_phone' => $phone,
            'message' => $message,
            'from' => $from,
        ],
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: multipart/form-data',
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
    if (curl_errno($curl)) {
        curl_close($curl);
        throw new Exception('cURL error: ' . curl_error($curl));
    }

    curl_close($curl);
    
    echo "Single SMS HTTP Code: $httpCode\n";
    echo "Single SMS Response: $response\n";
    
    return json_decode($response, true);
}

try {
    echo "=== Тест API Eskiz для массовой отправки SMS ===\n\n";
    
    // 1. Логинимся
    echo "1. Логинимся в API Eskiz...\n";
    $token = eskizLogin($eskizLogin, $eskizPassword);
    echo "Токен получен: " . substr($token, 0, 20) . "...\n\n";
    
    // 2. Тестируем одиночную отправку
    echo "2. Тестируем одиночную отправку SMS...\n";
    $singleResult = sendSingleSms($token, '998901234567', 'Test single message', $smsNickName);
    echo "\n";
    
    // 3. Тестируем массовую отправку
    echo "3. Тестируем массовую отправку SMS...\n";
    $messages = [
        [
            'user_sms_id' => '1',
            'to' => '998901234567',
            'text' => 'Test batch message 1',
        ],
        [
            'user_sms_id' => '2', 
            'to' => '998901234568',
            'text' => 'Test batch message 2',
        ]
    ];
    
    $batchResult = sendBatchSms($token, $messages, $smsNickName);
    echo "\n";
    
    echo "=== Тест завершен ===\n";
    
} catch (Exception $e) {
    echo "Ошибка: " . $e->getMessage() . "\n";
}
