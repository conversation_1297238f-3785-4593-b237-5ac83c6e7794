<?php

namespace app\modules\backend\controllers;

use app\common\models\Clients;
use app\common\models\Region;
use app\common\services\EskizBulkSmsService;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;

/**
 * Контроллер для управления массовыми SMS рассылками
 */
class BulkSmsController extends BaseController
{


    /**
     * Главная страница массовых SMS
     */
    public function actionIndex()
    {
        $eskizBulkSmsService = new EskizBulkSmsService();

        // Проверяем доступность API Eskiz
        $serviceAvailable = $eskizBulkSmsService->isServiceAvailable();

        // Получаем регионы для фильтров
        $regions = Region::find()
            ->where(['deleted_at' => null])
            ->orderBy('name')
            ->all();

        return $this->render('index', [
            'serviceAvailable' => $serviceAvailable,
            'regions' => $regions,
        ]);
    }

    /**
     * Создание новой кампании
     */
    public function actionCreateCampaign()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $request = Yii::$app->request;
        $message = $request->post('message');
        $clientIds = $request->post('client_ids', []);
        $useTemplate = (bool)$request->post('use_template', true);

        if (empty($message)) {
            return ['success' => false, 'message' => 'Заполните текст сообщения'];
        }

        if (empty($clientIds)) {
            return ['success' => false, 'message' => 'Выберите получателей'];
        }

        try {
            // Получаем клиентов
            $clients = Clients::find()
                ->where(['id' => $clientIds])
                ->andWhere(['!=', 'phone_number', ''])
                ->andWhere(['status' => 1])
                ->andWhere(['deleted_at' => null])
                ->all();

            if (empty($clients)) {
                return ['success' => false, 'message' => 'Не найдено активных клиентов с номерами телефонов'];
            }

            // Формируем список получателей
            $recipients = [];
            $eskizBulkSmsService = new EskizBulkSmsService();

            foreach ($clients as $client) {
                $phone = $eskizBulkSmsService->formatPhone($client->phone_number);

                if ($eskizBulkSmsService->validatePhone($phone)) {
                    $recipients[] = [
                        'client_id' => $client->id,
                        'phone' => $phone,
                        'name' => $client->full_name ?: 'Клиент',
                    ];
                }
            }

            if (empty($recipients)) {
                return ['success' => false, 'message' => 'Не найдено клиентов с валидными номерами телефонов'];
            }

            // Отправляем SMS
            $result = $eskizBulkSmsService->sendBulkSms($message, $recipients, $useTemplate);

            return [
                'success' => true,
                'message' => 'SMS отправлены успешно',
                'sent_count' => $result['sent_count'] ?? count($recipients),
                'recipients_count' => count($recipients),
            ];
        } catch (\Exception $e) {
            Yii::error('Ошибка создания кампании: ' . $e->getMessage(), 'bulk-sms');
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Создание кампании по фильтрам
     */
    public function actionCreateCampaignByFilter()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $request = Yii::$app->request;
        $name = $request->post('name');
        $message = $request->post('message');
        $regionIds = $request->post('region_ids', []);
        $status = $request->post('status', 1);
        $useTemplate = (bool)$request->post('use_template', true);

        if (empty($message)) {
            return ['success' => false, 'message' => 'Заполните текст сообщения'];
        }

        try {
            $filters = [
                'status' => (int)$status,
                'has_phone' => true,
            ];

            if (!empty($regionIds)) {
                $filters['region_ids'] = array_map('intval', $regionIds);
            }

            $eskizBulkSmsService = new EskizBulkSmsService();
            $result = $eskizBulkSmsService->sendBulkSmsByFilter($name, $message, $filters, $useTemplate);

            return [
                'success' => true,
                'message' => 'SMS по фильтру отправлены успешно',
                'sent_count' => $result['sent_count'] ?? 0,
                'recipients_count' => $result['total_recipients'] ?? 0,
            ];
        } catch (\Exception $e) {
            Yii::error('Ошибка создания кампании по фильтру: ' . $e->getMessage(), 'bulk-sms');
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Отправка шаблона на модерацию
     */
    public function actionSubmitTemplate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $request = Yii::$app->request;
        $templateText = $request->post('template');

        if (empty($templateText)) {
            return ['success' => false, 'message' => 'Заполните текст шаблона'];
        }

        try {
            $eskizBulkSmsService = new EskizBulkSmsService();
            $result = $eskizBulkSmsService->submitTemplate($templateText);

            return [
                'success' => $result['success'],
                'message' => $result['message'],
                'result' => $result['result'] ?? null
            ];
        } catch (\Exception $e) {
            Yii::error('Ошибка отправки шаблона на модерацию: ' . $e->getMessage(), 'bulk-sms');
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Проверка статуса API Eskiz
     */
    public function actionCheckStatus()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $eskizBulkSmsService = new EskizBulkSmsService();
            $serviceAvailable = $eskizBulkSmsService->isServiceAvailable();

            return [
                'success' => true,
                'service_available' => $serviceAvailable,
                'message' => $serviceAvailable ? 'API Eskiz доступен' : 'API Eskiz недоступен'
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }



    /**
     * Поиск клиентов для рассылки
     */
    public function actionSearchClients()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $query = Yii::$app->request->get('q', '');
        $limit = (int)Yii::$app->request->get('limit', 20);

        if (strlen($query) < 2) {
            return ['results' => []];
        }

        $clients = Clients::find()
            ->where(['like', 'full_name', $query])
            ->andWhere(['!=', 'phone_number', ''])
            ->andWhere(['status' => 1])
            ->andWhere(['deleted_at' => null])
            ->limit($limit)
            ->all();

        $results = [];
        foreach ($clients as $client) {
            $results[] = [
                'id' => $client->id,
                'text' => $client->full_name . ' (' . $client->phone_number . ')',
                'phone' => $client->phone_number,
            ];
        }

        return ['results' => $results];
    }
}
