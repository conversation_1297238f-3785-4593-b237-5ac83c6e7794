<?php

namespace app\modules\backend\controllers;

use app\common\models\Clients;
use app\common\models\Region;
use app\common\services\BulkSmsService;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;
use yii\data\ArrayDataProvider;

/**
 * Контроллер для управления массовыми SMS рассылками
 */
class BulkSmsController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
        ];
    }

    /**
     * Главная страница массовых SMS
     */
    public function actionIndex()
    {
        $bulkSmsService = new BulkSmsService();
        
        // Проверяем доступность Go сервиса
        $serviceAvailable = $bulkSmsService->isServiceAvailable();
        
        // Получаем список кампаний
        $campaigns = [];
        if ($serviceAvailable) {
            try {
                $campaignsData = $bulkSmsService->getCampaigns(1, 10);
                $campaigns = $campaignsData['data']['campaigns'] ?? [];
            } catch (\Exception $e) {
                Yii::$app->session->addFlash('error', 'Ошибка получения кампаний: ' . $e->getMessage());
            }
        }

        // Получаем статистику очереди
        $queueStats = null;
        if ($serviceAvailable) {
            try {
                $queueStatsData = $bulkSmsService->getQueueStatus();
                $queueStats = $queueStatsData['data'] ?? null;
            } catch (\Exception $e) {
                // Игнорируем ошибки статистики
            }
        }

        // Получаем регионы для фильтров
        $regions = Region::find()
            ->where(['deleted_at' => null])
            ->orderBy('name')
            ->all();

        return $this->render('index', [
            'serviceAvailable' => $serviceAvailable,
            'campaigns' => $campaigns,
            'queueStats' => $queueStats,
            'regions' => $regions,
        ]);
    }

    /**
     * Создание новой кампании
     */
    public function actionCreateCampaign()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $request = Yii::$app->request;
        $name = $request->post('name');
        $message = $request->post('message');
        $clientIds = $request->post('client_ids', []);
        
        if (empty($name) || empty($message)) {
            return ['success' => false, 'message' => 'Заполните название и текст сообщения'];
        }

        if (empty($clientIds)) {
            return ['success' => false, 'message' => 'Выберите получателей'];
        }

        try {
            // Получаем клиентов
            $clients = Clients::find()
                ->where(['id' => $clientIds])
                ->andWhere(['!=', 'phone_number', ''])
                ->andWhere(['status' => 1])
                ->andWhere(['deleted_at' => null])
                ->all();

            if (empty($clients)) {
                return ['success' => false, 'message' => 'Не найдено активных клиентов с номерами телефонов'];
            }

            // Формируем список получателей
            $recipients = [];
            $bulkSmsService = new BulkSmsService();
            
            foreach ($clients as $client) {
                $phone = $bulkSmsService->formatPhone($client->phone_number);
                
                if ($bulkSmsService->validatePhone($phone)) {
                    $recipients[] = [
                        'client_id' => $client->id,
                        'phone' => $phone,
                        'name' => $client->full_name ?: 'Клиент',
                    ];
                }
            }

            if (empty($recipients)) {
                return ['success' => false, 'message' => 'Не найдено клиентов с валидными номерами телефонов'];
            }

            // Создаем кампанию
            $result = $bulkSmsService->createCampaign($name, $message, $recipients);

            return [
                'success' => true,
                'message' => 'Кампания создана успешно',
                'campaign_id' => $result['data']['id'] ?? null,
                'recipients_count' => count($recipients),
            ];

        } catch (\Exception $e) {
            Yii::error('Ошибка создания кампании: ' . $e->getMessage(), 'bulk-sms');
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Создание кампании по фильтрам
     */
    public function actionCreateCampaignByFilter()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $request = Yii::$app->request;
        $name = $request->post('name');
        $message = $request->post('message');
        $regionIds = $request->post('region_ids', []);
        $status = $request->post('status', 1);

        if (empty($name) || empty($message)) {
            return ['success' => false, 'message' => 'Заполните название и текст сообщения'];
        }

        try {
            $filters = [
                'status' => (int)$status,
                'has_phone' => true,
            ];

            if (!empty($regionIds)) {
                $filters['region_ids'] = array_map('intval', $regionIds);
            }

            $bulkSmsService = new BulkSmsService();
            $result = $bulkSmsService->createCampaignByFilter($name, $message, $filters);

            return [
                'success' => true,
                'message' => 'Кампания по фильтру создана успешно',
                'campaign_id' => $result['data']['id'] ?? null,
                'recipients_count' => $result['data']['total_recipients'] ?? 0,
            ];

        } catch (\Exception $e) {
            Yii::error('Ошибка создания кампании по фильтру: ' . $e->getMessage(), 'bulk-sms');
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Получение статуса кампании
     */
    public function actionCampaignStatus($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        try {
            $bulkSmsService = new BulkSmsService();
            $status = $bulkSmsService->getCampaignStatus($id);
            
            return ['success' => true, 'data' => $status['data'] ?? []];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Запуск кампании
     */
    public function actionStartCampaign($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        try {
            $bulkSmsService = new BulkSmsService();
            $result = $bulkSmsService->startCampaign($id);
            
            return [
                'success' => true,
                'message' => 'Кампания запущена успешно',
                'data' => $result['data'] ?? []
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Остановка кампании
     */
    public function actionStopCampaign($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        try {
            $bulkSmsService = new BulkSmsService();
            $result = $bulkSmsService->stopCampaign($id);
            
            return [
                'success' => true,
                'message' => 'Кампания остановлена успешно',
                'data' => $result['data'] ?? []
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Список кампаний
     */
    public function actionCampaigns()
    {
        $page = (int)Yii::$app->request->get('page', 1);
        $limit = (int)Yii::$app->request->get('limit', 20);
        $status = Yii::$app->request->get('status');

        try {
            $bulkSmsService = new BulkSmsService();
            $result = $bulkSmsService->getCampaigns($page, $limit, $status ? (int)$status : null);
            
            $campaigns = $result['data']['campaigns'] ?? [];
            
            // Создаем провайдер данных для пагинации
            $dataProvider = new ArrayDataProvider([
                'allModels' => $campaigns,
                'pagination' => [
                    'pageSize' => $limit,
                    'totalCount' => $result['data']['total'] ?? 0,
                ],
            ]);

            return $this->render('campaigns', [
                'dataProvider' => $dataProvider,
                'totalCount' => $result['data']['total'] ?? 0,
            ]);

        } catch (\Exception $e) {
            Yii::$app->session->addFlash('error', 'Ошибка получения кампаний: ' . $e->getMessage());
            
            $dataProvider = new ArrayDataProvider([
                'allModels' => [],
                'pagination' => ['pageSize' => $limit],
            ]);

            return $this->render('campaigns', [
                'dataProvider' => $dataProvider,
                'totalCount' => 0,
            ]);
        }
    }

    /**
     * Поиск клиентов для рассылки
     */
    public function actionSearchClients()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $query = Yii::$app->request->get('q', '');
        $limit = (int)Yii::$app->request->get('limit', 20);
        
        if (strlen($query) < 2) {
            return ['results' => []];
        }

        $clients = Clients::find()
            ->where(['like', 'full_name', $query])
            ->andWhere(['!=', 'phone_number', ''])
            ->andWhere(['status' => 1])
            ->andWhere(['deleted_at' => null])
            ->limit($limit)
            ->all();

        $results = [];
        foreach ($clients as $client) {
            $results[] = [
                'id' => $client->id,
                'text' => $client->full_name . ' (' . $client->phone_number . ')',
                'phone' => $client->phone_number,
            ];
        }

        return ['results' => $results];
    }
}
